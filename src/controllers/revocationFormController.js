const { Revocation } = require("../models/revocation");
const XLSX = require("xlsx");
const nodemailer = require("nodemailer");
const fs = require("fs");

// Submit single revocation
const submitRevocation = async (req, res) => {
  try {
    const {
      fullName,
      employeeId,
      jobTitle,
      department,
      contact,
      systems,
      reason,
      otherReason,
      approverName,
      approverJobTitle,
      approverContact,
    } = req.body;

    const parsedSystems =
      typeof systems === "string" ? JSON.parse(systems) : systems;

    const signaturePath = req.file ? req.file.path : null;

    const revocation = await Revocation.create({
      fullName,
      employeeId,
      jobTitle,
      department,
      contact,
      systems: parsedSystems,
      reason,
      otherReason,
      approverName,
      approverJobTitle,
      approverContact,
      signaturePath,
    });

    res.status(201).json({
      message: "Revocation submitted successfully",
      data: revocation,
    });
  } catch (error) {
    console.error("Revocation error:", error);
    res.status(500).json({ error: "Failed to submit revocation" });
  }
};

// Get all pending revocations
const getPendingRevocations = async (req, res) => {
  try {
    const pending = await Revocation.findAll({
      where: { status: "Pending" },
      order: [["createdAt", "DESC"]],
    });
    res.status(200).json({ data: pending });
  } catch (error) {
    console.error("Error fetching pending revocations:", error);
    res.status(500).json({ error: "Failed to fetch pending revocations" });
  }
};

// IT approves revocation
const approveRevocation = async (req, res) => {
  try {
    const { id } = req.params;
    const revocation = await Revocation.findByPk(id);
    if (!revocation) return res.status(404).json({ error: "Not found" });

    revocation.status = "Approved";
    revocation.actionedAt = new Date();
    revocation.actionedByIT = "IT ADMIN";

    await revocation.save();
    res.status(200).json({ message: "Revocation approved", data: revocation });
  } catch (error) {
    console.error("Error approving revocation:", error);
    res.status(500).json({ error: "Failed to approve revocation" });
  }
};

// IT rejects revocation
const rejectRevocation = async (req, res) => {
  try {
    const { id } = req.params;
    const { rejectionReason } = req.body;
    const revocation = await Revocation.findByPk(id);
    if (!revocation) return res.status(404).json({ error: "Not found" });

    revocation.status = "Rejected";
    revocation.rejectionReason = rejectionReason;
    revocation.actionedAt = new Date();
    revocation.actionedByIT = "IT ADMIN";

    await revocation.save();
    res.status(200).json({ message: "Revocation rejected", data: revocation });
  } catch (error) {
    console.error("Error rejecting revocation:", error);
    res.status(500).json({ error: "Failed to reject revocation" });
  }
};

// Get full audit trail
const getAllRevocations = async (req, res) => {
  try {
    const all = await Revocation.findAll({
      order: [["createdAt", "DESC"]],
    });
    res.status(200).json({ data: all });
  } catch (error) {
    console.error("Error fetching all revocations:", error);
    res.status(500).json({ error: "Failed to fetch audit records" });
  }
};

// Get completed revocations (Approved or Rejected)
const getCompletedRevocations = async (req, res) => {
  try {
    const completed = await Revocation.findAll({
      where: {
        status: ["Approved", "Rejected"],
      },
      order: [["actionedAt", "DESC"]],
    });
    res.status(200).json({ data: completed });
  } catch (error) {
    console.error("Error fetching completed revocations:", error);
    res.status(500).json({ error: "Failed to fetch processed revocations" });
  }
};

// BULK UPLOAD revocations + Email IT Admin
const transporter = nodemailer.createTransport({
  service: "gmail",
  auth: {
    user: process.env.IT_EMAIL_USER,
    pass: process.env.IT_EMAIL_PASS,
  },
});

const bulkUploadRevocations = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: "No file uploaded" });
    }

    const filePath = req.file.path;
    const workbook = XLSX.readFile(filePath);
    const sheet = workbook.Sheets[workbook.SheetNames[0]];
    const rows = XLSX.utils.sheet_to_json(sheet);

    const revocations = [];

    for (const row of rows) {
      const fullName =
        `${row["First Name"] || ""} ${row["Middle Name"] || ""} ${row["Surname"] || ""}`.trim();
      const employeeId = row["Payroll No."] || "";

      if (!fullName || !employeeId) continue;

      const entry = await Revocation.create({
        fullName,
        employeeId,
        jobTitle: row["Position"] || "",
        department: row["Branch"] || "",
        contact: row["Phone No."] || "",
        systems: [row["Deactivate"] || "All systems & Communication channels"],
        reason: "Resignation/Termination",
        approverName: "HR Officer",
        approverJobTitle: "HR",
        approverContact: "N/A",
        status: "Pending",
      });

      revocations.push(entry);
    }

    // Compose improved email message
    const emailBody = `
      <h3 style="margin-bottom: 8px;">User Access Revocation Notification</h3>
      <p>Dear IT Administrator,</p>

      <p>This is to formally notify you that <strong>${revocations.length}</strong> user(s) have been submitted by HR for system access revocation.</p>

      <p>Please review and take the necessary deactivation actions.</p>

      <p>For your convenience, the list of affected employees is attached in the Excel file.</p>

      <p>Regards,<br />
      <strong>HR Access Management System</strong></p>
    `;

    await transporter.sendMail({
      from: `"HR Portal" <${process.env.IT_EMAIL_USER}>`,
      to: process.env.IT_EMAIL_RECEIVER,
      subject: "New Revocation Submission from HR",
      html: emailBody,
      attachments: [
        {
          filename: req.file.originalname,
          path: filePath,
        },
      ],
    });

    res.status(200).json({
      message: "Bulk upload successful. IT Admin notified.",
      count: revocations.length,
    });
  } catch (error) {
    console.error("Bulk upload error:", error);
    res.status(500).json({ error: "Failed to process bulk upload" });
  }
};

module.exports = {
  submitRevocation,
  getPendingRevocations,
  approveRevocation,
  rejectRevocation,
  getAllRevocations,
  getCompletedRevocations,
  bulkUploadRevocations,
};
