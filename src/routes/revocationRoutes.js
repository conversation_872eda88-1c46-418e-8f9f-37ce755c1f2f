const express = require("express");
const router = express.Router();

const { uploadSignature, uploadExcel } = require("../_middlewares/upload");

const {
  submitRevocation,
  getPendingRevocations,
  approveRevocation,
  rejectRevocation,
  getAllRevocations,
  getCompletedRevocations,
  bulkUploadRevocations,
} = require("../controllers/revocationFormController");

// HR submits revocation form with signature (single)
router.post("/submit", uploadSignature.single("signature"), submitRevocation);

// HR bulk uploads Excel file
router.post("/bulk-upload", uploadExcel.single("file"), bulkUploadRevocations);

router.get("/pending", getPendingRevocations);
router.put("/:id/approve", approveRevocation);
router.put("/:id/reject", rejectRevocation);

router.get("/audit", getAllRevocations);
router.get("/completed", getCompletedRevocations);

module.exports = router;
